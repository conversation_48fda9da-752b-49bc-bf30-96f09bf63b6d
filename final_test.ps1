Write-Host "=== Stage 7 Comprehensive Test ===" -ForegroundColor Cyan

# Test 1: Create a file and test serving it (matching the stage example)
Write-Host "`n1. Testing file creation and serving (matching stage example)" -ForegroundColor Yellow
$testContent = "Hello, <PERSON>!"
[System.IO.File]::WriteAllText("foo", $testContent, [System.Text.Encoding]::ASCII)

try {
    $response = Invoke-WebRequest -Uri "http://localhost:4221/files/foo" -UseBasicParsing
    Write-Host "✓ Status: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor Green
    Write-Host "✓ Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor Green
    Write-Host "✓ Content-Length: $($response.Headers['Content-Length'])" -ForegroundColor Green
    
    # Convert byte response back to string for verification
    $actualContent = [System.Text.Encoding]::ASCII.GetString($response.Content)
    if ($actualContent -eq $testContent) {
        Write-Host "✓ Content matches expected: '$actualContent'" -ForegroundColor Green
    } else {
        Write-Host "✗ Content mismatch. Expected: '$testContent', Got: '$actualContent'" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Non-existent file (matching stage example)
Write-Host "`n2. Testing non-existent file (matching stage example)" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:4221/files/non_existant_file" -UseBasicParsing
    Write-Host "✗ Expected 404 but got: $($response.StatusCode)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq "NotFound") {
        Write-Host "✓ Correctly returned 404 Not Found" -ForegroundColor Green
    } else {
        Write-Host "✗ Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 3: Verify all headers are correct
Write-Host "`n3. Testing exact header compliance" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:4221/files/foo" -UseBasicParsing
    
    $expectedHeaders = @{
        "Content-Type" = "application/octet-stream"
        "Content-Length" = "13"
    }
    
    $allHeadersCorrect = $true
    foreach ($header in $expectedHeaders.Keys) {
        $actualValue = $response.Headers[$header]
        $expectedValue = $expectedHeaders[$header]
        if ($actualValue -eq $expectedValue) {
            Write-Host "✓ ${header}: $actualValue" -ForegroundColor Green
        } else {
            Write-Host "✗ ${header}: Expected '$expectedValue', Got '$actualValue'" -ForegroundColor Red
            $allHeadersCorrect = $false
        }
    }
    
    if ($allHeadersCorrect) {
        Write-Host "✓ All headers are correct" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Header test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Binary file handling
Write-Host "`n4. Testing binary file handling" -ForegroundColor Yellow
$binaryData = [byte[]](0..255)
[System.IO.File]::WriteAllBytes("binary_test.bin", $binaryData)

try {
    $response = Invoke-WebRequest -Uri "http://localhost:4221/files/binary_test.bin" -UseBasicParsing
    Write-Host "✓ Binary file status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "✓ Binary file Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor Green
    Write-Host "✓ Binary file Content-Length: $($response.Headers['Content-Length'])" -ForegroundColor Green
    
    if ($response.Content.Length -eq 256) {
        Write-Host "✓ Binary content length correct: $($response.Content.Length)" -ForegroundColor Green
    } else {
        Write-Host "✗ Binary content length incorrect: $($response.Content.Length)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Binary test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Cyan
Write-Host "Stage 7 implementation testing completed." -ForegroundColor White
