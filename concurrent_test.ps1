$jobs = @()

# Start multiple concurrent requests
for ($i = 1; $i -le 5; $i++) {
    $job = Start-Job -ScriptBlock {
        param($testNum)
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:4221/files/test_file1.txt" -UseBasicParsing
            "Test $testNum - Status: $($response.StatusCode), Length: $($response.Headers['Content-Length'])"
        } catch {
            "Test $testNum - Error: $($_.Exception.Message)"
        }
    } -ArgumentList $i
    $jobs += $job
}

# Wait for all jobs to complete and collect results
$results = $jobs | Wait-Job | Receive-Job
$jobs | Remove-Job

Write-Host "=== Concurrent Test Results ==="
$results | ForEach-Object { Write-Host $_ }
