// HTTP Server - Stage 6: Concurrent Connections
// Handles multiple simultaneous client connections using async/await and Task.Run

using System.Net;
using System.Net.Sockets;
using System.Text;

class Program
{
    static async Task Main(string[] args)
    {
        IPAddress ipAddress = IPAddress.Loopback;
        int port = 4221;
        TcpListener server = new TcpListener(ipAddress, port);

        try
        {
            server.Start();
            Console.WriteLine($"Server started. Listening on {ipAddress}:{port}");

            // Main server loop - accepts connections continuously
            while (true)
            {
                // Accept new client connection asynchronously
                TcpClient client = await server.AcceptTcpClientAsync();
                Console.WriteLine("Client connected.");

                // CONCURRENT HANDLING: Process each client on a separate thread
                // This allows the server to immediately accept the next connection
                // while the current one is being handled in the background
                _ = Task.Run(() => HandleClient(client));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
        }
        finally
        {
            server.Stop();
        }
    }

    /// <summary>
    /// Handles individual client connections asynchronously.
    /// Each client is processed independently on a separate thread.
    /// </summary>
    private static async Task HandleClient(TcpClient client)
    {
        try
        {
            await using NetworkStream stream = client.GetStream();

            // Read the HTTP request from the client
            byte[] buffer = new byte[1024];
            int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
            string requestString = Encoding.ASCII.GetString(buffer, 0, bytesRead);
            Console.WriteLine($"Received request:\n{requestString}");

            // Parse the HTTP request line to extract the path
            string[] requestLines = requestString.Split("\r\n");
            string requestLine = requestLines[0];
            string[] requestLineParts = requestLine.Split(' ');
            string path = requestLineParts[1];

            // Parse HTTP headers into a case-insensitive dictionary
            var headers = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            for (int i = 1; i < requestLines.Length; i++)
            {
                string headerLine = requestLines[i];
                if (string.IsNullOrEmpty(headerLine))
                {
                    break; // Empty line separates headers from body
                }

                int separatorIndex = headerLine.IndexOf(": ");
                if (separatorIndex > 0)
                {
                    string key = headerLine.Substring(0, separatorIndex);
                    string value = headerLine.Substring(separatorIndex + 2);
                    headers[key] = value;
                }
            }

            // Route the request and generate appropriate HTTP response
            string httpResponse;

            if (path.StartsWith("/echo/"))
            {
                // Echo endpoint: return the path content as response body
                string content = path.Substring("/echo/".Length);
                httpResponse = $"HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: {content.Length}\r\n\r\n{content}";
                Console.WriteLine($"Responding with '200 OK' and body '{content}' for path '{path}'.");
            }
            else if (path == "/user-agent")
            {
                // User-Agent endpoint: return the User-Agent header value
                string userAgent = headers.GetValueOrDefault("User-Agent", "Unknown");
                httpResponse = $"HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: {userAgent.Length}\r\n\r\n{userAgent}";
                Console.WriteLine($"Responding with User-Agent: {userAgent}");
            }
            else if (path == "/")
            {
                // Root endpoint: return simple 200 OK
                httpResponse = "HTTP/1.1 200 OK\r\n\r\n";
                Console.WriteLine("Responding with '200 OK' for path '/'.");
            }
            else
            {
                // All other paths: return 404 Not Found
                httpResponse = "HTTP/1.1 404 Not Found\r\n\r\n";
                Console.WriteLine($"Responding with '404 Not Found' for path '{path}'.");
            }

            // Send the HTTP response back to the client
            byte[] responseBytes = Encoding.ASCII.GetBytes(httpResponse);
            await stream.WriteAsync(responseBytes, 0, responseBytes.Length);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling client: {ex.Message}");
        }
        finally
        {
            // Ensure client connection is properly closed
            client.Close();
            Console.WriteLine("Client disconnected.");
        }
    }
}
