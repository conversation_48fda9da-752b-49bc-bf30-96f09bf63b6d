import socket
import sys

def test_http_request(path):
    try:
        # Create socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('127.0.0.1', 4221))
        
        # Send HTTP request
        request = f"GET {path} HTTP/1.1\r\nHost: localhost\r\n\r\n"
        sock.send(request.encode('ascii'))
        
        # Receive response
        response = sock.recv(4096)
        sock.close()

        print(f"=== Testing {path} ===")

        # Find the end of headers (double CRLF)
        header_end = response.find(b'\r\n\r\n')
        if header_end == -1:
            print("Error: Could not find end of headers")
            return response

        headers_bytes = response[:header_end]
        body_bytes = response[header_end + 4:]  # Skip the \r\n\r\n

        # Parse headers
        headers_str = headers_bytes.decode('ascii', errors='ignore')
        lines = headers_str.split('\r\n')

        print("Status Line:", lines[0])
        for i in range(1, len(lines)):
            if lines[i]:
                print("Header:", lines[i])

        # Show body
        if len(body_bytes) > 0:
            try:
                body_str = body_bytes.decode('ascii', errors='ignore')
                print("Body (as text):", repr(body_str))
            except:
                print("Body (binary):", body_bytes[:50], "..." if len(body_bytes) > 50 else "")
            print("Body Length:", len(body_bytes))
        
        print()
        return response
        
    except Exception as e:
        print(f"Error testing {path}: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_http_request(sys.argv[1])
    else:
        # Test multiple endpoints
        test_http_request("/files/test_file1.txt")
        test_http_request("/files/test_file2.txt")
        test_http_request("/files/nonexistent.txt")
        test_http_request("/")
        test_http_request("/echo/hello")
        test_http_request("/user-agent")
