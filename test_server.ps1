function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$Description
    )
    
    Write-Host "=== Testing: $Description ===" -ForegroundColor Yellow
    Write-Host "URL: $Url"
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -UseBasicParsing
        Write-Host "Status: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor Green
        Write-Host "Content-Type: $($response.Headers['Content-Type'])"
        Write-Host "Content-Length: $($response.Headers['Content-Length'])"
        
        if ($response.Content) {
            Write-Host "Content: '$($response.Content)'"
            Write-Host "Actual Length: $($response.Content.Length)"
        }
    }
    catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
    Write-Host ""
}

# Wait a moment for server to start
Start-Sleep -Seconds 2

# Test file endpoints
Test-HttpEndpoint "http://localhost:4221/files/test_file1.txt" "Existing file 1"
Test-HttpEndpoint "http://localhost:4221/files/test_file2.txt" "Existing file 2"
Test-HttpEndpoint "http://localhost:4221/files/nonexistent.txt" "Non-existent file"

# Test existing endpoints
Test-HttpEndpoint "http://localhost:4221/" "Root endpoint"
Test-HttpEndpoint "http://localhost:4221/echo/hello" "Echo endpoint"
Test-HttpEndpoint "http://localhost:4221/user-agent" "User-Agent endpoint"
